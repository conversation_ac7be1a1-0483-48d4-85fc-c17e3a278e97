# Company Shared Folder Deployment Guide

This guide will help you deploy and test the Project Management Application on your company shared folder.

## 📍 Target Location
```
O:\MCT Aviation\8110 Airport Projects (OPD)\Taskmaster
```

## Prerequisites

### 1. Network Access
- ✅ Connected to company network
- ✅ O: drive is mapped and accessible
- ✅ Read/write permissions to the target folder

### 2. Verify Folder Access
1. Open Windows Explorer
2. Navigate to: `O:\MCT Aviation\8110 Airport Projects (OPD)\Taskmaster`
3. Ensure you can create/delete files in this folder

## Step-by-Step Deployment

### Step 1: Prepare the Environment

1. **Open Command Prompt/PowerShell as Administrator**
   ```cmd
   # Navigate to your project directory
   cd C:\Users\<USER>\OneDrive\Desktop\project-management-app
   ```

2. **Verify Current Configuration**
   ```cmd
   # Check that .env.local is configured for company share
   type .env.local
   ```
   Should show: `SHARED_PATH=O:/MCT Aviation/8110 Airport Projects (OPD)/Taskmaster`

### Step 2: Deploy to Company Share

1. **Run Deployment Script**
   ```cmd
   npm run deploy:company
   ```

   This will:
   - ✅ Check access to company shared folder
   - ✅ Create required directories (uploads, backups)
   - ✅ Initialize SQLite database on shared folder
   - ✅ Create deployment info file

2. **Expected Output**
   ```
   🚀 Deploying Project Management Application to Company Share
   📍 Target Location: O:/MCT Aviation/8110 Airport Projects (OPD)/Taskmaster

   🔍 Checking access to company shared folder...
   ✅ Company shared folder is accessible
   📁 Path: O:/MCT Aviation/8110 Airport Projects (OPD)/Taskmaster

   📁 Creating required directories...
   ✅ Created directory: O:/MCT Aviation/8110 Airport Projects (OPD)/Taskmaster/uploads
   ✅ Created directory: O:/MCT Aviation/8110 Airport Projects (OPD)/Taskmaster/backups

   🗄️  Initializing database on company share...
   ✅ Database tables created successfully
   ✅ Database initialized successfully
   📍 Database location: O:/MCT Aviation/8110 Airport Projects (OPD)/Taskmaster/data.sqlite

   📄 Creating deployment info file...
   ✅ Deployment info saved: O:/MCT Aviation/8110 Airport Projects (OPD)/Taskmaster/deployment-info.json

   🎉 Deployment completed successfully!
   ```

### Step 3: Migrate Your Data

1. **Import Data from Firebase**
   ```cmd
   npm run migrate:firebase
   ```

   This will:
   - ✅ Connect to Firebase
   - ✅ Download all departments, users, and projects
   - ✅ Import data into the company shared database
   - ✅ Create project logs

2. **Verify Data Migration**
   ```cmd
   npm run show:data
   ```

### Step 4: Start the Application

1. **Start Development Server**
   ```cmd
   npm run dev
   ```

2. **Access the Application**
   - Open browser: http://localhost:3002 (or 3000)
   - Login with migrated credentials

### Step 5: Test Functionality

1. **Test Database Operations**
   - ✅ View projects page
   - ✅ View departments page
   - ✅ View team page
   - ✅ View analytics dashboard
   - ✅ View activity logs

2. **Test File Operations**
   - ✅ Try uploading a file to a project
   - ✅ Verify file is saved to: `O:\MCT Aviation\8110 Airport Projects (OPD)\Taskmaster\uploads`

3. **Test Multi-User Access**
   - ✅ Have colleagues access the same shared database
   - ✅ Verify concurrent access works properly

## Folder Structure After Deployment

```
O:\MCT Aviation\8110 Airport Projects (OPD)\Taskmaster\
├── data.sqlite                 # Main database file
├── deployment-info.json        # Deployment information
├── uploads\                    # File uploads directory
│   └── [project files]
└── backups\                    # Database backups
    └── [backup files]
```

## Troubleshooting

### Issue 1: Cannot Access Shared Folder
**Error**: `Company shared folder not accessible`

**Solutions**:
1. Ensure you're connected to company network
2. Check if O: drive is mapped:
   ```cmd
   net use O:
   ```
3. Re-map the drive if needed:
   ```cmd
   net use O: \\server\share /persistent:yes
   ```

### Issue 2: Permission Denied
**Error**: `EACCES: permission denied`

**Solutions**:
1. Run Command Prompt as Administrator
2. Check folder permissions with IT department
3. Ensure you have write access to the target folder

### Issue 3: Database Lock Error
**Error**: `database is locked`

**Solutions**:
1. Close any other instances of the application
2. Check if someone else is using the database
3. Restart the application

### Issue 4: Migration Fails
**Error**: `Migration failed`

**Solutions**:
1. Check internet connection for Firebase access
2. Verify Firebase credentials
3. Try running migration again

## Production Considerations

### 1. Backup Strategy
- Database backups are automatically created in `backups\` folder
- Consider scheduled backups for production use

### 2. User Access
- All users need access to the shared folder
- Consider setting up proper Windows permissions

### 3. Performance
- SQLite performs well on network shares for small teams
- Monitor performance with multiple concurrent users

### 4. Security
- Shared folder should have appropriate access controls
- Consider encrypting sensitive data

## Support Commands

```bash
# Deploy to company share
npm run deploy:company

# Migrate data from Firebase
npm run migrate:firebase

# Show current data
npm run show:data

# Start application
npm run dev

# Initialize fresh database (if needed)
npm run init:db
```

## Success Indicators

✅ **Deployment Successful** when you see:
- Company shared folder accessible
- Database created on shared folder
- All directories created
- Migration completed with real data
- Application starts without errors
- Can access all pages with real data

---

**Ready for Company Testing!** 🚀

Your Project Management Application is now deployed on the company shared folder and ready for team use.
