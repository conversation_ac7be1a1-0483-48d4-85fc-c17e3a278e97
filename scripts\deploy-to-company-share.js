// <PERSON>ript to deploy and initialize the application on company shared folder
const fs = require('fs');
const path = require('path');
const Database = require('better-sqlite3');

// Company shared folder configuration
const COMPANY_CONFIG = {
  SHARED_PATH: 'O:/MCT Aviation/8110 Airport Projects (OPD)/Taskmaster',
  DATABASE_NAME: 'data.sqlite',
  UPLOADS_FOLDER: 'uploads',
  BACKUPS_FOLDER: 'backups'
};

const COMPANY_PATHS = {
  ROOT: COMPANY_CONFIG.SHARED_PATH,
  DATABASE: path.join(COMPANY_CONFIG.SHARED_PATH, COMPANY_CONFIG.DATABASE_NAME),
  UPLOADS: path.join(COMPANY_CONFIG.SHARED_PATH, COMPANY_CONFIG.UPLOADS_FOLDER),
  BACKUPS: path.join(COMPANY_CONFIG.SHARED_PATH, COMPANY_CONFIG.BACKUPS_FOLDER)
};

function checkAccess() {
  console.log('🔍 Checking access to company shared folder...\n');
  
  try {
    // Check if the main path exists and is accessible
    if (!fs.existsSync(COMPANY_PATHS.ROOT)) {
      throw new Error(`Company shared folder not accessible: ${COMPANY_PATHS.ROOT}`);
    }
    
    // Test write permissions
    const testFile = path.join(COMPANY_PATHS.ROOT, 'access-test.tmp');
    fs.writeFileSync(testFile, 'test');
    fs.unlinkSync(testFile);
    
    console.log('✅ Company shared folder is accessible');
    console.log(`📁 Path: ${COMPANY_PATHS.ROOT}`);
    
    return true;
  } catch (error) {
    console.error('❌ Cannot access company shared folder:', error.message);
    console.log('\n💡 Troubleshooting:');
    console.log('   1. Ensure you are connected to the company network');
    console.log('   2. Check if the O: drive is mapped correctly');
    console.log('   3. Verify you have read/write permissions to the folder');
    console.log('   4. Try accessing the folder in Windows Explorer first');
    return false;
  }
}

function createDirectories() {
  console.log('\n📁 Creating required directories...');
  
  const directories = [
    COMPANY_PATHS.UPLOADS,
    COMPANY_PATHS.BACKUPS
  ];
  
  directories.forEach(dir => {
    try {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
      } else {
        console.log(`✅ Directory exists: ${dir}`);
      }
    } catch (error) {
      console.error(`❌ Failed to create directory ${dir}:`, error.message);
      throw error;
    }
  });
}

function initializeDatabase() {
  console.log('\n🗄️  Initializing database on company share...');
  
  try {
    // Check if database already exists
    if (fs.existsSync(COMPANY_PATHS.DATABASE)) {
      console.log('⚠️  Database already exists. Creating backup...');
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(COMPANY_PATHS.BACKUPS, `data-backup-${timestamp}.sqlite`);
      
      fs.copyFileSync(COMPANY_PATHS.DATABASE, backupPath);
      console.log(`✅ Backup created: ${backupPath}`);
    }
    
    // Initialize database
    const db = new Database(COMPANY_PATHS.DATABASE);
    db.pragma('foreign_keys = ON');
    
    // Create tables
    createTables(db);
    
    console.log('✅ Database initialized successfully');
    console.log(`📍 Database location: ${COMPANY_PATHS.DATABASE}`);
    
    db.close();
    
  } catch (error) {
    console.error('❌ Failed to initialize database:', error.message);
    throw error;
  }
}

function createTables(database) {
  // Users table
  database.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password TEXT,
      role TEXT DEFAULT 'USER',
      department TEXT,
      departmentId TEXT,
      phone TEXT,
      bio TEXT,
      jobTitle TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (departmentId) REFERENCES departments(id) ON DELETE SET NULL
    )
  `);

  // Departments table
  database.exec(`
    CREATE TABLE IF NOT EXISTS departments (
      id TEXT PRIMARY KEY,
      name TEXT UNIQUE NOT NULL,
      description TEXT,
      budget REAL,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Projects table
  database.exec(`
    CREATE TABLE IF NOT EXISTS projects (
      id TEXT PRIMARY KEY,
      projectTitle TEXT NOT NULL,
      drivers TEXT,
      type TEXT,
      year INTEGER,
      opdFocal TEXT,
      capex REAL,
      opex REAL,
      status TEXT DEFAULT 'Planning',
      subStatus TEXT,
      department TEXT,
      departmentId TEXT,
      area TEXT,
      awardedCompany TEXT,
      savings REAL,
      percentage INTEGER DEFAULT 0,
      startDate DATETIME,
      endDate DATETIME,
      dateOfReceiveFinalDoc DATETIME,
      quarterOfYear TEXT DEFAULT 'Q1',
      column1 TEXT,
      createdBy TEXT,
      statusChangeNote TEXT,
      statusChangeDate DATETIME,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (departmentId) REFERENCES departments(id) ON DELETE SET NULL
    )
  `);

  // Project logs table
  database.exec(`
    CREATE TABLE IF NOT EXISTS project_logs (
      id TEXT PRIMARY KEY,
      projectId TEXT NOT NULL,
      action TEXT NOT NULL,
      field TEXT,
      oldValue TEXT,
      newValue TEXT,
      note TEXT,
      userId TEXT,
      userName TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (projectId) REFERENCES projects(id) ON DELETE CASCADE
    )
  `);

  // Project files table
  database.exec(`
    CREATE TABLE IF NOT EXISTS project_files (
      id TEXT PRIMARY KEY,
      projectId TEXT NOT NULL,
      fileName TEXT NOT NULL,
      originalName TEXT NOT NULL,
      filePath TEXT NOT NULL,
      fileSize INTEGER,
      mimeType TEXT,
      uploadedBy TEXT,
      uploadedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (projectId) REFERENCES projects(id) ON DELETE CASCADE
    )
  `);

  console.log('✅ Database tables created successfully');
}

function createInfoFile() {
  console.log('\n📄 Creating deployment info file...');
  
  const deploymentInfo = {
    deployedAt: new Date().toISOString(),
    deployedBy: process.env.USERNAME || 'Unknown',
    version: '1.0.0',
    environment: 'Company Shared Folder',
    paths: COMPANY_PATHS,
    instructions: {
      start: 'Run "npm run dev" from the project directory',
      access: 'Application will be available at http://localhost:3000',
      database: 'SQLite database is stored on the shared folder',
      uploads: 'File uploads are stored on the shared folder'
    }
  };
  
  const infoPath = path.join(COMPANY_PATHS.ROOT, 'deployment-info.json');
  fs.writeFileSync(infoPath, JSON.stringify(deploymentInfo, null, 2));
  
  console.log(`✅ Deployment info saved: ${infoPath}`);
}

async function deployToCompanyShare() {
  console.log('🚀 Deploying Project Management Application to Company Share\n');
  console.log(`📍 Target Location: ${COMPANY_PATHS.ROOT}\n`);
  
  try {
    // Step 1: Check access
    if (!checkAccess()) {
      process.exit(1);
    }
    
    // Step 2: Create directories
    createDirectories();
    
    // Step 3: Initialize database
    initializeDatabase();
    
    // Step 4: Create deployment info
    createInfoFile();
    
    console.log('\n🎉 Deployment completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('   1. Run "npm run migrate:firebase" to import your data');
    console.log('   2. Run "npm run dev" to start the application');
    console.log('   3. Access the application at http://localhost:3000');
    console.log('\n💾 Data Storage:');
    console.log(`   Database: ${COMPANY_PATHS.DATABASE}`);
    console.log(`   Uploads: ${COMPANY_PATHS.UPLOADS}`);
    console.log(`   Backups: ${COMPANY_PATHS.BACKUPS}`);
    
  } catch (error) {
    console.error('\n❌ Deployment failed:', error.message);
    process.exit(1);
  }
}

// Run deployment
deployToCompanyShare();
